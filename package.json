{"name": "hydrogen-quickstart", "private": true, "sideEffects": false, "version": "2024.7.10", "scripts": {"env": "shopify hydrogen env pull", "build": "shopify hydrogen build --codegen", "dev": "shopify hydrogen dev --codegen", "debug": "npm run dev -- --debug", "preview": "shopify hydrogen preview --build", "lint": "eslint --no-error-on-unmatched-pattern --ext .js,.ts,.jsx,.tsx .", "codegen": "shopify hydrogen codegen", "deploy": "shopify hydrogen deploy"}, "prettier": "@shopify/prettier-config", "dependencies": {"@material-tailwind/react": "^2.1.10", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@remix-run/react": "^2.13.1", "@remix-run/server-runtime": "^2.13.1", "@shopify/hydrogen": "^2024.10.0", "@shopify/remix-oxygen": "^2.0.9", "@tanstack/react-table": "^8.20.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "embla-carousel-autoplay": "^8.5.1", "embla-carousel-react": "^8.5.1", "graphql": "^16.6.0", "graphql-tag": "^2.12.6", "isbot": "^3.8.0", "lucide-react": "^0.541.0", "motion": "^12.0.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-intersection-observer": "^9.14.1", "semver": "^7.7.2", "tailwind-merge": "^3.0.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@graphql-codegen/cli": "5.0.7", "@remix-run/dev": "^2.13.1", "@remix-run/eslint-config": "^2.13.1", "@shopify/cli": "^3.74.1", "@shopify/hydrogen-codegen": "^0.3.2", "@shopify/mini-oxygen": "^3.1.0", "@shopify/oxygen-workers-types": "^4.1.2", "@shopify/prettier-config": "^1.1.2", "@total-typescript/ts-reset": "^0.6.0", "@types/eslint": "^9.0.0", "@types/react": "^18.2.22", "@types/react-dom": "^18.2.7", "autoprefixer": "^10.4.20", "eslint": "^8.20.0", "eslint-plugin-hydrogen": "0.12.3", "postcss": "^8.4.49", "prettier": "^3.0.0", "tailwindcss": "^3.4.17", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}}