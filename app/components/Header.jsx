import {Suspense, useState} from 'react';
import {motion, AnimatePresence} from 'motion/react';
import {Await, Link, NavLink, useAsyncValue} from '@remix-run/react';
import {useAnalytics, useOptimisticCart, Image} from '@shopify/hydrogen';
import {useAside} from '~/components/Aside';
import {Separator} from '~/components/ui/separator';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuSub,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuViewport,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from '~/components/ui/navigation-menu';
import logoSquare from '~/assets/logo_square.png';
import iconSearch from '~/assets/search.svg';
import iconCart from '~/assets/cart.svg';
import iconAccount from '~/assets/account.svg';
import {CaretDown} from '~/components/common/Caret';
import {Loading} from '~/components/Loading';
import {getMenuUrl} from '~/lib/utils';
import {NavMenu} from '~/components/NavMenu';
import {ImageCard} from '~/components/ImageCard';
import {cn} from '~/lib/utils';

/**
 * @param {HeaderProps}
 */
export function Header({header, customer, socials, cart}) {
  const {shop, menu} = header;
  return (
    <header className="header shadow">
      <div className="header-main">
        <Link to="/" className="flex py-2 mr-4">
          <img
            src={logoSquare}
            alt="logo"
            className="!w-[150px] md:!w-[200px] max-h-full"
          />
        </Link>
        <HeaderMenuDesktop menu={menu} />
        <HeaderCtas customer={customer} cart={cart} />
      </div>
    </header>
  );
}

function HeaderMenuDesktop({menu}) {
  const [selected, setSelected] = useState(null);
  return <NavMenu menu={menu}></NavMenu>;
}

export function HeaderMenuMobile({menu}) {
  const {close} = useAside();
  const [selectedItem, setSelectedItem] = useState(null);
  const [selectedSubItem, setSelectedSubItem] = useState(null);
  return (
    <nav className="header-menu-mobile" role="navigation">
      {(menu || FALLBACK_HEADER_MENU).items.map((item) => {
        const subItems = item.items || [];
        const hasSubItems = subItems.length > 0;
        const isSelectedItem = selectedItem?.title === item.title;

        return (
          <div key={item.id}>
            {/* Category Header */}
            <ParentLink
              item={item}
              showCaret={hasSubItems}
              isActive={isSelectedItem}
              onClickLink={close}
              onClickCaret={() => {
                setSelectedSubItem(null);
                if (isSelectedItem) {
                  setSelectedItem(null);
                  return;
                }
                setSelectedItem(item);
              }}
              className={cn(
                'text-xl font-medium',
                isSelectedItem && 'text-gba-orange',
                'transition-colors',
              )}
              caretClassName="mt-1"
            ></ParentLink>
            {/* Sub category header */}
            {isSelectedItem && selectedSubItem && (
              <ParentLink
                item={selectedSubItem}
                showCaret={
                  selectedSubItem.items && selectedSubItem.items.length > 0
                }
                isActive={isSelectedItem}
                onClickLink={close}
                onClickCaret={() => {
                  setSelectedSubItem(null);
                }}
                className={cn(
                  'font-medium p-1',
                  isSelectedItem && 'text-gba-orange',
                  'transition-colors',
                )}
              ></ParentLink>
            )}
            {/* Main category image */}
            {/* {hasSubItems && !isSelectedItem && (
              <AnimatePresence>
                <motion.div
                  initial={{opacity: 0}}
                  animate={{opacity: 1}}
                  exit={{opacity: 0}}
                  className="w-[50vw]"
                >
                  <ImageCard
                    to={getMenuUrl(item)}
                    imageData={item?.resource?.image}
                    onClick={close}
                  ></ImageCard>
                </motion.div>
              </AnimatePresence>
            )} */}
            {/* Subcategories */}
            {isSelectedItem && !selectedSubItem && (
              <div className="flex flex-col products-grid">
                {subItems.map((item) => {
                  const subSubItems = item.items || [];
                  const hasSubSubItems = subSubItems.length > 0;
                  const imageData = item?.resource?.image;

                  return (
                    <motion.div
                      variants={{
                        initial: {opacity: 0},
                        animate: {opacity: 1, transition: {delay: 0.1}},
                      }}
                      initial="initial"
                      animate="animate"
                      key={item.id}
                      className="flex flex-col items-center"
                    >
                      <ImageCard
                        to={getMenuUrl(item)}
                        title={item.title}
                        imageData={imageData}
                        onClick={close}
                      ></ImageCard>
                      {hasSubSubItems && (
                        <button
                          onClick={() => {
                            setSelectedSubItem(item);
                          }}
                        >
                          <CaretDown active={false} className="w-4 h-4" />
                        </button>
                      )}
                    </motion.div>
                  );
                })}
              </div>
            )}
            {/* Sub subcategories */}
            {isSelectedItem && selectedSubItem && (
              <div className="flex flex-col products-grid">
                {selectedSubItem?.items.map((item) => {
                  const subSubItems = item.items || [];
                  const imageData = item?.resource?.image;

                  return (
                    <motion.div
                      variants={{
                        initial: {opacity: 0},
                        animate: {opacity: 1, transition: {delay: 0.1}},
                      }}
                      initial="initial"
                      animate="animate"
                      key={item.id}
                      className="flex flex-col items-center"
                    >
                      <ImageCard
                        to={getMenuUrl(item)}
                        title={item.title}
                        imageData={imageData}
                        onClick={close}
                      ></ImageCard>
                    </motion.div>
                  );
                })}
              </div>
            )}
          </div>
        );
      })}
    </nav>
  );
}

function ParentLink({
  className,
  caretClassName,
  item,
  showCaret,
  isActive,
  onClickLink,
  onClickCaret,
}) {
  return (
    <AnimatePresence>
      <motion.div
        initial={{opacity: 0}}
        animate={{opacity: 1}}
        className={cn('flex items-center', className)}
      >
        <NavLink
          end
          onClick={onClickLink}
          prefetch="intent"
          style={activeLinkStyle}
          to={getMenuUrl(item)}
          className=""
        >
          {item.title}
        </NavLink>
        {showCaret && (
          <button onClick={onClickCaret}>
            <CaretDown
              active={isActive}
              className={cn('px-1', caretClassName)}
            />
          </button>
        )}
      </motion.div>
    </AnimatePresence>
  );
}

/**
 * @param {Pick<HeaderProps, 'isLoggedIn' | 'cart'>}
 */
function HeaderCtas({customer, cart}) {
  return (
    <nav className="header-ctas" role="navigation">
      <HeaderMenuMobileToggle />
      <SearchToggle />
      <Suspense>
        <Await resolve={customer}>
          {({status: {isLoggedIn, isApproved}}) =>
            isLoggedIn ? (
              <>
                <Link prefetch="intent" to="/account" className="">
                  <img src={iconAccount} alt="account" className="h-[33px]" />
                </Link>
                {isApproved ? (
                  <CartToggle cart={cart} />
                ) : (
                  <CartBadge count={-1} />
                )}
              </>
            ) : (
              <Link
                prefetch="intent"
                to="/account"
                className="bg-gba-orange round py-2 px-4 w-fit text-white"
              >
                Login
              </Link>
            )
          }
        </Await>
      </Suspense>
    </nav>
  );
}

function HeaderMenuMobileToggle() {
  const {open} = useAside();
  return (
    <button
      className="header-menu-mobile-toggle"
      onClick={() => open('mobile')}
    >
      <h3 className="text-3xl text-black">☰</h3>
    </button>
  );
}

function SearchToggle() {
  const {open} = useAside();
  return (
    <button onClick={() => open('search')}>
      <img src={iconSearch} alt="search" className="h-[33px]" />
    </button>
  );
}

/**
 * @param {{count: number | null}}
 */
function CartBadge({count}) {
  const {open} = useAside();
  const {publish, shop, cart, prevCart} = useAnalytics();
  const disabled = count < 0;

  return (
    <a
      href="/cart"
      onClick={(e) => {
        e.preventDefault();
        open('cart');
        publish('cart_viewed', {
          cart,
          prevCart,
          shop,
          url: window.location.href || '',
        });
      }}
      className={`relative flex flex-row ${disabled && 'pointer-events-none cursor-not-allowed opacity-20'}`}
    >
      <img src={iconCart} alt="cart" className="h-[33px]" />
      {!disabled && (
        <p className="absolute -right-2 -top-2 h-[25px] w-[25px] bg-gba-blue bg-opacity-80 rounded-full text-white text-center">
          {count === null ? <span>&nbsp;</span> : count}
        </p>
      )}
    </a>
  );
}

/**
 * @param {Pick<HeaderProps, 'cart'>}
 */
function CartToggle({cart}) {
  return (
    <Suspense fallback={<CartBadge count={null} />}>
      <Await resolve={cart}>
        <CartBanner />
      </Await>
    </Suspense>
  );
}

function CartBanner() {
  const originalCart = useAsyncValue();
  const cart = useOptimisticCart(originalCart);
  return <CartBadge count={cart?.totalQuantity ?? 0} />;
}

const FALLBACK_HEADER_MENU = {
  id: 'gid://shopify/Menu/199655587896',
  items: [
    {
      id: 'gid://shopify/MenuItem/461609500728',
      resourceId: null,
      tags: [],
      title: 'Collections',
      type: 'HTTP',
      url: '/collections',
      items: [],
    },
    {
      id: 'gid://shopify/MenuItem/461609533496',
      resourceId: null,
      tags: [],
      title: 'Blog',
      type: 'HTTP',
      url: '/blogs/journal',
      items: [],
    },
    {
      id: 'gid://shopify/MenuItem/461609566264',
      resourceId: null,
      tags: [],
      title: 'Policies',
      type: 'HTTP',
      url: '/policies',
      items: [],
    },
    {
      id: 'gid://shopify/MenuItem/461609599032',
      resourceId: 'gid://shopify/Page/92591030328',
      tags: [],
      title: 'About',
      type: 'PAGE',
      url: '/pages/about',
      items: [],
    },
  ],
};

/**
 * @param {{
 *   isActive: boolean;
 *   isPending: boolean;
 * }}
 */
function activeLinkStyle({isActive, isPending}) {
  return {
    fontWeight: isActive ? '600' : undefined,
    // textDecoration: isActive ? 'underline' : 'none',
    //color: isActive ? '#F9911E' : 'black',
    // color: isPending ? 'grey' : 'black',
  };
}

/** @typedef {'desktop' | 'mobile'} Viewport */
/**
 * @typedef {Object} HeaderProps
 * @property {HeaderQuery} header
 * @property {Promise<CartApiQueryFragment|null>} cart
 * @property {Promise<boolean>} isLoggedIn
 * @property {string} publicStoreDomain
 */

/** @typedef {import('@shopify/hydrogen').CartViewPayload} CartViewPayload */
/** @typedef {import('storefrontapi.generated').HeaderQuery} HeaderQuery */
/** @typedef {import('storefrontapi.generated').CartApiQueryFragment} CartApiQueryFragment */
