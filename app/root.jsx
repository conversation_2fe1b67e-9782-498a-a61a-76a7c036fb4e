import {useNonce, getShopAnalytics, Analytics} from '@shopify/hydrogen';
import {defer} from '@shopify/remix-oxygen';
import {LiveReload} from '@remix-run/react';
import {
  Links,
  Meta,
  Outlet,
  Scripts,
  useRouteError,
  useRouteLoaderData,
  ScrollRestoration,
  isRouteErrorResponse,
} from '@remix-run/react';
import favicon from '~/assets/logo_square.png';
import resetStyles from '~/styles/reset.css?url';
import appStyles from '~/styles/app.css?url';
import {PageLayout} from '~/components/PageLayout';
import {FOOTER_QUERY, HEADER_QUERY, METAOBJECTS_QUERY} from '~/lib/fragments';
import {
  CUSTOMER_DETAILS_QUERY,
  CUSTOMER_STATUS_QUERY,
} from '~/graphql/customer-account/CustomerDetailsQuery';

/**
 * This is important to avoid re-fetching root queries on sub-navigations
 * @type {ShouldRevalidateFunction}
 */
export const shouldRevalidate = ({
  formMethod,
  currentUrl,
  nextUrl,
  defaultShouldRevalidate,
}) => {
  // revalidate when a mutation is performed e.g add to cart, login...
  if (formMethod && formMethod !== 'GET') return true;

  // revalidate when manually revalidating via useRevalidator
  if (currentUrl.toString() === nextUrl.toString()) return true;

  return defaultShouldRevalidate;
};

export function links() {
  return [
    {rel: 'stylesheet', href: resetStyles},
    {rel: 'stylesheet', href: appStyles},
    {
      rel: 'preconnect',
      href: 'https://cdn.shopify.com',
    },
    {
      rel: 'preconnect',
      href: 'https://shop.app',
    },
    {rel: 'icon', type: 'image/svg+xml', href: favicon},
  ];
}

/**
 * @param {LoaderFunctionArgs} args
 */
export async function loader(args) {
  // Start fetching non-critical data without blocking time to first byte
  const deferredData = loadDeferredData(args);

  // Await the critical data required to render initial state of the page
  const criticalData = await loadCriticalData(args);

  const {storefront, env} = args.context;

  return defer({
    ...deferredData,
    ...criticalData,
    publicStoreDomain: env.PUBLIC_STORE_DOMAIN,
    shop: getShopAnalytics({
      storefront,
      publicStorefrontId: env.PUBLIC_STOREFRONT_ID,
    }),
    consent: {
      checkoutDomain: env.PUBLIC_CHECKOUT_DOMAIN,
      storefrontAccessToken: env.PUBLIC_STOREFRONT_API_TOKEN,
      withPrivacyBanner: true,
      // localize the privacy banner
      country: args.context.storefront.i18n.country,
      language: args.context.storefront.i18n.language,
    },
  });
}

/**
 * Load data necessary for rendering content above the fold. This is the critical data
 * needed to render the page. If it's unavailable, the whole page should 400 or 500 error.
 * @param {LoaderFunctionArgs}
 */
async function loadCriticalData({params, context}) {
  const {storefront} = context;

  const [mainMenu, footerMenu] = await Promise.all([
    storefront.query(HEADER_QUERY, {
      cache: storefront.CacheLong(),
      variables: {
        headerMenuHandle: 'main-menu',
      },
    }),
    storefront.query(FOOTER_QUERY, {
      cache: storefront.CacheLong(),
      variables: {
        footerMenuHandle: 'footer',
      },
    }),
  ]);

  return {
    mainMenu,
    footerMenu,
  };
}

/**
 * Load data for rendering content below the fold. This data is deferred and will be
 * fetched after the initial page load. If it's unavailable, the page should still 200.
 * Make sure to not throw any errors here, as it will cause the page to 500.
 * @param {LoaderFunctionArgs}
 */
function loadDeferredData({context}) {
  const {storefront, customerAccount, cart} = context;

  const isLoggedInPromise = customerAccount.isLoggedIn();

  const customer = isLoggedInPromise
    .then((isLoggedIn) => {
      // This chained promise checks first if the user is logged in
      if (isLoggedIn) {
        return customerAccount
          .query(CUSTOMER_DETAILS_QUERY)
          .then(({data, errors}) => {
            const details = {};
            for (const [key, value] of Object.entries(data?.customer)) {
              if (key === 'emailAddress') {
                details['email'] = value?.emailAddress;
              } else if (key === 'addresses') {
                details['addresses'] = value?.nodes;
              } else if (key === 'metafields') {
                for (const metafield of value) {
                  if (metafield) {
                    const {key: mKey, value: mValue} = metafield;
                    if (typeof mValue === 'string' && mValue.length) {
                      details[mKey] = mValue;
                    }
                  }
                }
              } else {
                details[key] = value;
              }
            }

            const status = {
              isLoggedIn: true,
              isPending: false,
              isApproved: false,
            };
            if (details.status) {
              status.isPending = details.status.toLowerCase() === 'pending';
              status.isApproved = details.status.toLowerCase() === 'approved';
            }

            return {
              status,
              details,
            };
          });
      }

      return {
        status: {
          isLoggedIn: false,
        },
      };
    })
    .catch((error) => {
      console.error('Error fetching customer details', error);
      return {
        status: {
          isLoggedIn: false,
        },
        error: error.message,
      };
    });

  const brands = storefront
    .query(METAOBJECTS_QUERY, {
      cache: context.storefront.CacheLong(),
      variables: {
        type: 'brand',
      },
    })
    .then((response) => {
      const nodes = response?.metaobjects?.nodes;
      if (nodes) {
        return nodes.map(({fields}) => {
          const data = {};
          fields.forEach(({key, reference}) => {
            if (key === 'image') {
              data['id'] = reference.image.id;
              data['alt'] = reference.image.altText;
              data['url'] = reference.image.url;
            }
          });

          return data;
        });
      }
      // Something went wrong
      return response;
    })
    .catch((error) => {
      console.error(error);
      return error;
    });

  const catalogues = storefront
    .query(METAOBJECTS_QUERY, {
      cache: context.storefront.CacheLong(),
      variables: {
        type: 'catalogue',
      },
    })
    .then((response) => {
      const nodes = response?.metaobjects?.nodes;
      if (nodes) {
        //return nodes;
        return nodes.map(({fields}) => {
          const data = {};
          fields.forEach(({key, reference, value}) => {
            if (key === 'display_image') {
              data['id'] = reference.image.id;
              data['alt'] = reference.image.altText;
              data['url'] = reference.image.url;
            }
            if (key === 'link') {
              const link = JSON.parse(value);
              data['link_text'] = link.text;
              data['link_url'] = link.url;
            }
          });
          return data;
        });
      }
      // Something went wrong
      return response;
    });

  const socials = storefront
    .query(METAOBJECTS_QUERY, {
      cache: context.storefront.CacheLong(),
      variables: {
        type: 'social_media',
      },
    })
    .then((response) => {
      const nodes = response?.metaobjects?.nodes;
      if (nodes) {
        return nodes.map(({fields}) => {
          const data = {};
          fields.forEach(({key, reference, value}) => {
            if (key === 'logo') {
              data['image'] = reference.image;
              data['imageId'] = reference.image.id;
              data['imageAlt'] = reference.image.altText;
              data['imageUrl'] = reference.image.url;
            }
            if (key === 'link') {
              const link = JSON.parse(value);
              data['linkText'] = link.text;
              data['linkUrl'] = link.url;
            }
          });
          return data;
        });
      }
      // Something went wrong
      return response;
    });

  return {
    cart: cart.get(),
    customer,
    brands,
    catalogues,
    socials,
  };
}

/**
 * @param {{children?: React.ReactNode}}
 */
export function Layout({children}) {
  const nonce = useNonce();
  /** @type {RootLoader} */
  const data = useRouteLoaderData('root');

  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        <Meta />
        <Links />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="true"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
          rel="stylesheet"
        />
      </head>
      <body className="font-default">
        {data ? (
          <Analytics.Provider
            cart={data.cart}
            shop={data.shop}
            consent={data.consent}
          >
            <PageLayout {...data}>{children}</PageLayout>
          </Analytics.Provider>
        ) : (
          children
        )}
        <LiveReload origin="http://localhost:3000" />
        <ScrollRestoration nonce={nonce} />
        <Scripts nonce={nonce} />
      </body>
    </html>
  );
}

function App() {
  return <Outlet />;
}

export default App;

export function ErrorBoundary() {
  const error = useRouteError();
  let errorMessage = 'Unknown error';
  let errorStatus = 500;

  if (isRouteErrorResponse(error)) {
    errorMessage = error?.data?.message ?? error.data;
    errorStatus = error.status;
  } else if (error instanceof Error) {
    errorMessage = error.message;
  }

  return (
    <div className="route-error">
      <h1>Oops</h1>
      <h2>{errorStatus}</h2>
      {errorMessage && (
        <fieldset>
          <pre>{errorMessage}</pre>
        </fieldset>
      )}
    </div>
  );
}

/** @typedef {LoaderReturnData} RootLoader */

/** @typedef {import('@shopify/remix-oxygen').LoaderFunctionArgs} LoaderFunctionArgs */
/** @typedef {import('@remix-run/react').ShouldRevalidateFunction} ShouldRevalidateFunction */
/** @typedef {import('@shopify/remix-oxygen').SerializeFrom<typeof loader>} LoaderReturnData */
