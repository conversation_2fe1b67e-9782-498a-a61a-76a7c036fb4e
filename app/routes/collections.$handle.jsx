import {defer, redirect} from '@shopify/remix-oxygen';
import {useState, useEffect} from 'react';
import {Await, useLoaderData, useRouteLoaderData, Link} from '@remix-run/react';
import {getPaginationVariables, Analytics, Money} from '@shopify/hydrogen';
import {useVariantUrl} from '~/lib/variants';
import {PaginatedResourceSection} from '~/components/PaginatedResourceSection';
import {Breadcrumbs} from '~/components/Breadcrumbs';
import {ProductFilters} from '~/components/ProductFilters';
import {ProductPrice} from '~/components/ProductPrice';
import {ImageCard} from '~/components/ImageCard';
import {Suspense} from 'react';
import {HEADER_QUERY} from '~/lib/fragments';
import {Loading} from '~/components/Loading';
import {findBreadcrumbsByHandle} from '~/lib/utils';

/**
 * @type {MetaFunction<typeof loader>}
 */
export const meta = ({data}) => {
  return [
    {title: `${data?.collection.title ?? ''} | Glove Box Australia Pty Ltd`},
  ];
};

/**
 * @param {LoaderFunctionArgs} args
 */
export async function loader(args) {
  // Start fetching non-critical data without blocking time to first byte
  const deferredData = loadDeferredData(args);

  // Await the critical data required to render initial state of the page
  const criticalData = await loadCriticalData(args);

  return defer({...deferredData, ...criticalData});
}

/**
 * Load data necessary for rendering content above the fold. This is the critical data
 * needed to render the page. If it's unavailable, the whole page should 400 or 500 error.
 * @param {LoaderFunctionArgs}
 */
async function loadCriticalData({context, params, request}) {
  const {handle} = params;
  const {storefront} = context;
  const paginationVariables = getPaginationVariables(request, {
    pageBy: 10,
  });

  if (!handle) {
    throw redirect('/collections');
  }
  const url = new URL(request.url);
  const filterParams = url.searchParams.get('filters');
  const urlFilters = filterParams ? JSON.parse(filterParams) : [];
  const filters = urlFilters.map(([name, value]) => {
    if (name === 'tag') {
      return {
        tag: value,
      };
    }

    const [namespace, key] = name.split('.');
    if (namespace && key) {
      return {
        productMetafield: {
          namespace,
          key,
          value,
        },
      };
    }
  });

  const [{collection}, {menu}] = await Promise.all([
    storefront.query(COLLECTION_QUERY, {
      variables: {
        handle,
        filters,
        ...paginationVariables,
      },
    }),
    storefront.query(HEADER_QUERY, {
      variables: {
        headerMenuHandle: 'main-menu',
      },
    }),
  ]);

  if (!collection) {
    throw new Response(`Collection ${handle} not found`, {
      status: 404,
    });
  }

  const breadcrumbs = findBreadcrumbsByHandle(menu.items, handle);

  return {
    collection,
    breadcrumbs,
    urlFilters,
  };
}

/**
 * Load data for rendering content below the fold. This data is deferred and will be
 * fetched after the initial page load. If it's unavailable, the page should still 200.
 * Make sure to not throw any errors here, as it will cause the page to 500.
 * @param {LoaderFunctionArgs}
 */
function loadDeferredData({context}) {
  return {};
}

export default function Collection() {
  const {customer} = useRouteLoaderData('root');
  /** @type {LoaderReturnData} */
  const {collection, breadcrumbs, urlFilters} = useLoaderData();
  //console.log(collection);

  return (
    <div className="collection">
      <div className="collection-header rounded-b-2xl">
        <Breadcrumbs breadcrumbs={breadcrumbs} />
        {collection.handle !== 'specials' && (
          <ProductFilters
            activeFilters={urlFilters}
            filters={collection?.products?.filters}
          />
        )}
      </div>
      <div className="flex flex-col items-center max-w-[800px] p-2">
        <h1 className="font-medium text-xl md:text-2xl">{collection?.title}</h1>
        <p>{collection?.description}</p>
      </div>
      <PaginatedResourceSection
        connection={collection.products}
        resourcesClassName="products-grid"
      >
        {({node: product, index}) => {
          return (
            <ProductItem
              key={product.id}
              product={product}
              loading={index < 10 ? 'eager' : undefined}
              customer={customer}
            />
          );
        }}
      </PaginatedResourceSection>
      <Analytics.CollectionView
        data={{
          collection: {
            id: collection.id,
            handle: collection.handle,
          },
        }}
      />
    </div>
  );
}

/**
 * @param {{
 *   product: ProductItemFragment;
 *   loading?: 'eager' | 'lazy';
 * }}
 */
function ProductItem({product, loading, customer}) {
  const minQuantity = product?.moq?.value ? parseInt(moq.value, 10) : 1;
  const variant = product.variants.nodes[0];
  const variantUrl = useVariantUrl(product.handle, variant.selectedOptions);
  const onSale = product?.tags.includes('Specials');

  return (
    <ImageCard
      key={product.id}
      to={variantUrl}
      title={product.title}
      titleClassName="text-sm"
      imageData={product.featuredImage}
      onSale={onSale}
    >
      <small className="text-lg">
        <Suspense fallback={<Loading />}>
          <Await resolve={customer}>
            {({status: {isLoggedIn, isApproved}}) =>
              isLoggedIn &&
              isApproved && (
                <ProductPrice
                  price={variant?.price}
                  compareAtPrice={variant?.compareAtPrice}
                />
              )
            }
          </Await>
        </Suspense>
      </small>
    </ImageCard>
  );
}

const PRODUCT_ITEM_FRAGMENT = `#graphql
  fragment MoneyProductItem on MoneyV2 {
    amount
    currencyCode
  }
  fragment ProductItem on Product {
    id
    handle
    title
    featuredImage {
      id
      altText
      url
      width
      height
    }
    priceRange {
      minVariantPrice {
        ...MoneyProductItem
      }
      maxVariantPrice {
        ...MoneyProductItem
      }
    }
    variants(first: 1) {
      nodes {
        price {
          amount
          currencyCode
        }
        compareAtPrice {
          amount
          currencyCode
        }
        selectedOptions {
          name
          value
        }
      }
    }
    tags
  }
`;

// NOTE: https://shopify.dev/docs/api/storefront/2022-04/objects/collection
const COLLECTION_QUERY = `#graphql
  ${PRODUCT_ITEM_FRAGMENT}
  query Collection(
    $handle: String!
    $country: CountryCode
    $language: LanguageCode
    $first: Int
    $last: Int
    $startCursor: String
    $endCursor: String
    $filters: [ProductFilter!]
  ) @inContext(country: $country, language: $language) {
    collection(handle: $handle) {
      id
      handle
      title
      description
      products(
        first: $first,
        last: $last,
        before: $startCursor,
        after: $endCursor
        sortKey: MANUAL,
        filters: $filters
      ) {
        nodes {
          ...ProductItem
        }
        filters {
          id
          label
          type
          values {
            id
            label
            count
            input
          }
        }
        pageInfo {
          hasPreviousPage
          hasNextPage
          endCursor
          startCursor
        }
      }
    }
  }
`;

/** @typedef {import('@shopify/remix-oxygen').LoaderFunctionArgs} LoaderFunctionArgs */
/** @template T @typedef {import('@remix-run/react').MetaFunction<T>} MetaFunction */
/** @typedef {import('storefrontapi.generated').ProductItemFragment} ProductItemFragment */
/** @typedef {import('@shopify/remix-oxygen').SerializeFrom<typeof loader>} LoaderReturnData */
